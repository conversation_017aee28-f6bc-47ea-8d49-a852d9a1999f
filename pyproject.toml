[project]
name = "tflaunch"
version = "0.1.0"
description = "A game launcher and manager for Team Fortress 2"
readme = "README.md"
requires-python = "==3.13.*"
authors = [{ name = "NecoGlitched", email = "<EMAIL>" }]
dependencies = [
    "loguru>=0.7.3",
    "nicegui[native]>=2.22.2",
    "pywebview[qt]>=5.4",
    "tomlkit>=0.13.3",
]

[tool.ruff]
fix = true
line-length = 88
cache-dir = "/tmp/ruff_cache"

[tool.ruff.lint]
select = ["ALL"]
extend-ignore = [
    "D100",    # Missing docstring in public module
    "D401",    # First line should be in imperative mood
    "DOC201",  # Missing `return` in docstring
    "PLR0913", # Too many arguments to function call
    "TD003",   # Missing TO-DO issue link
]

[tool.ruff.lint.per-file-ignores]
"src/app.py" = ["E402"]

[tool.mypy]
cache_dir = "/tmp/mypy_cache"
strict = true
