# Copyright (c) 2025 Neco Glitched
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program. If not, see <https://www.gnu.org/licenses/>.

# https://github.com/zauberzeug/nicegui/issues/1841#issuecomment-**********
import multiprocessing

multiprocessing.set_start_method("spawn", force=True)

from nicegui import ui

with ui.element("div").classes("flex w-full h-screen"):
    # Left Sidebar
    with ui.element("div").classes("w-[20%] max-w-xs bg-base-200 p-4"):
        ui.label("Left Sidebar").classes("text-xl")
    # Content
    with ui.element("div").classes("grow bg-base-100 p-4"):
        ui.label("Content").classes("text-xl")
    # Right Sidebar
    with ui.element("div").classes("w-[20%] max-w-xs bg-base-200 p-4"):
        ui.label("Right Sidebar").classes("text-xl")

ui.run(
    title="TF Launch",
    native=True,
)
