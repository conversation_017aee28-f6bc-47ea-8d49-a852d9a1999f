# Copyright (c) 2025 Neco Glitched
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program. If not, see <https://www.gnu.org/licenses/>.

"""Application configuration module."""

import os
import sys
from pathlib import Path

from loguru import logger

# Debug mode
DEBUG = os.getenv("TFLAUNCH_DEBUG", "0").lower() in {"1", "true", "yes"}

# Paths
ROOT = Path(__file__).parent.parent

# Configure loguru
logger.configure(
    handlers=[
        {
            "sink": sys.stdout,
            "level": "DEBUG" if DEBUG else "INFO",
            "format": (
                "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                "<level>{level: <8}</level> | "
                "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
                "<level>{message}</level>"
            ),
            "colorize": True,
            "backtrace": True,
            "diagnose": True,
        },
        {
            "sink": ROOT / "logs" / "tflaunch.log",
            "level": "INFO",
            "format": (
                "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | "
                "{name}:{function}:{line} - {message}"
            ),
            "compression": "tar.gz",
            "rotation": "1 MB",
            "retention": "5 days",
        },
    ],
)

if DEBUG:
    logger.warning("THIS IS ONLY A REMINDER: Debug mode is enabled.")
